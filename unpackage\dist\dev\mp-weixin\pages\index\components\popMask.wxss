@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.more_norm_pop.data-v-92d6e38c {
  width: calc(100vw - 160rpx);
  box-sizing: border-box;
  position: relative;
  top: 50%;
  left: 50%;
  padding: 40rpx;
  -webkit-transform: translateX(-50%) translateY(-50%);
          transform: translateX(-50%) translateY(-50%);
  background: #fff;
  border-radius: 20rpx;
}
.more_norm_pop .div_big_image.data-v-92d6e38c {
  width: 100%;
  border-radius: 10rpx;
}
.more_norm_pop .title.data-v-92d6e38c {
  font-size: 40rpx;
  line-height: 80rpx;
  text-align: center;
  font-weight: bold;
}
.more_norm_pop .items_cont.data-v-92d6e38c {
  display: flex;
  flex-wrap: wrap;
  margin-left: -14rpx;
  max-height: 50vh;
}
.more_norm_pop .items_cont .item_row .flavor_name.data-v-92d6e38c {
  height: 40rpx;
  opacity: 1;
  font-size: 28rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #666666;
  line-height: 40rpx;
  padding-left: 10rpx;
  padding-top: 20rpx;
}
.more_norm_pop .items_cont .item_row .flavor_item.data-v-92d6e38c {
  display: flex;
  flex-wrap: wrap;
}
.more_norm_pop .items_cont .item_row .flavor_item .item.data-v-92d6e38c {
  border: 1px solid #ffb302;
  border-radius: 12rpx;
  margin: 20rpx 10rpx;
  padding: 0 26rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  color: #333333;
}
.more_norm_pop .items_cont .item_row .flavor_item .act.data-v-92d6e38c {
  background: #ffc200;
  border: 1px solid #ffc200;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
}
.more_norm_pop .but_item.data-v-92d6e38c {
  display: flex;
  position: relative;
  flex: 1;
  padding-left: 10rpx;
  margin: 34rpx 0 -20rpx 0;
}
.more_norm_pop .but_item .price.data-v-92d6e38c {
  text-align: left;
  color: #e94e3c;
  line-height: 88rpx;
  box-sizing: border-box;
  font-size: 48rpx;
  font-family: DIN, DIN-Medium;
  font-weight: 500;
}
.more_norm_pop .but_item .price .ico.data-v-92d6e38c {
  font-size: 28rpx;
}
.more_norm_pop .but_item .active.data-v-92d6e38c {
  position: absolute;
  right: 0rpx;
  bottom: 20rpx;
  display: flex;
}
.more_norm_pop .but_item .active .dish_add.data-v-92d6e38c,
.more_norm_pop .but_item .active .dish_red.data-v-92d6e38c {
  display: block;
  width: 72rpx;
  height: 72rpx;
}
.more_norm_pop .but_item .active .dish_number.data-v-92d6e38c {
  padding: 0 10rpx;
  line-height: 72rpx;
  font-size: 30rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
}
.more_norm_pop .but_item .active .dish_card_add.data-v-92d6e38c {
  width: 200rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-weight: 500;
  font-size: 28rpx;
  opacity: 1;
  background: #ffc200;
  border-radius: 30rpx;
}
.close.data-v-92d6e38c {
  position: absolute;
  bottom: -180rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.close .close_img.data-v-92d6e38c {
  width: 88rpx;
  height: 88rpx;
}

