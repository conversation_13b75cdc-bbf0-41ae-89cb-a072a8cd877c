(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/addOrEditAddress/addOrEditAddress"],{2537:function(e,t,n){"use strict";n.r(t);var o=n("b580"),i=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=i.a},3142:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return o}));var o={uniNavBar:function(){return n.e("components/uni-nav-bar/uni-nav-bar").then(n.bind(null,"c455"))},uniEasyinput:function(){return n.e("uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(n.bind(null,"a0f8"))}},i=function(){var e=this.$createElement;this._self._c},r=[]},"35ee":function(e,t,n){},"4ad7":function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("6134");o(n("3240"));var i=o(n("fac4"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},b580:function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n("7ca3")),r=n("b370");function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var d={components:{simpleAddress:function(){Promise.all([n.e("common/vendor"),n.e("pages/common/simple-address/simple-address")]).then(function(){return resolve(n("abf3"))}.bind(null,n)).catch(n.oe)}},data:function(){return{platform:"ios",showDel:!1,showInput:!0,valueMan:!0,valueWoman:!0,showClass:!1,items:[{value:"0",name:"先生"},{value:"1",name:"女士"}],current:0,options:[{name:"公司",type:1},{name:"家",type:2},{name:"学校",type:3}],form:{name:"",phone:"",type:1,sex:"0",provinceCode:"11",provinceName:"",cityCode:"1101",cityName:"",districtCode:"110102",districtName:"",detail:""},cityPickerValueDefault:[0,0,1],pickerText:"",address:"",delId:""}},onLoad:function(t){this.init(),t&&"编辑"===t.type?(this.delId="",this.showDel=!0,e.setNavigationBarTitle({title:"编辑收获地址"}),this.delId=t.id,this.queryAddressBookById(t.id)):this.showDel=!1},onUnload:function(){e.removeStorage({key:"edit"})},computed:{statusBarHeight:function(){return e.getSystemInfoSync().statusBarHeight+"px"}},created:function(){},methods:{init:function(){var t=e.getSystemInfoSync();this.platform=t.platform},goBack:function(){e.redirectTo({url:"/pages/address/address"})},queryAddressBookById:function(e){var t=this;(0,r.queryAddressBookById)({id:e}).then((function(e){1===e.code&&(t.form={provinceCode:e.data.provinceCode,cityCode:e.data.cityCode,districtCode:e.data.districtCode,phone:e.data.phone,name:e.data.consignee,sex:e.data.sex,type:Number(e.data.label),detail:e.data.detail,id:e.data.id},e.data.provinceName&&e.data.cityName&&e.data.districtName&&(t.address=e.data.provinceName+"/"+e.data.cityName+"/"+e.data.districtName))}))},isClass:function(e){this.showClass=e},openAddres:function(){this.$refs.simpleAddress.open(),e.hideKeyboard()},onConfirm:function(e){this.form.provinceCode=e.provinceCode,this.form.cityCode=e.cityCode,this.form.districtCode=e.areaCode,this.address=e.label},bindTextAreaBlur:function(e){},radioChange:function(e){"man"===e.detail.value?this.form.radio=0:this.form.radio=1},sexChangeHandle:function(e){this.form.sex=e},addAddressFun:function(){if(""===this.form.name)return e.showToast({title:"联系人不能为空",duration:1e3,icon:"none"});if(""===this.form.phone)return e.showToast({title:"手机号不能为空",duration:1e3,icon:"none"});if(""===this.form.type)return e.showToast({title:"所属标签不能为空",duration:1e3,icon:"none"});if(""===this.address)return e.showToast({title:"所在地区不能为空",duration:1e3,icon:"none"});if(""===this.form.detail)return e.showToast({title:"详细地址不能为空不能为空",duration:1e3,icon:"none"});if(this.form.phone){if(!/^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(this.form.phone))return e.showToast({title:"手机号输入有误",duration:1e3,icon:"none"})}if(this.form.name){if(!/^[\u0391-\uFFE5A-Za-z0-9]{2,12}$/.test(this.form.name))return e.showToast({title:"请输入合法的2-12个字符",duration:1e3,icon:"none"})}var t=s(s({},this.form),{},{label:this.form.type,consignee:this.form.name,provinceName:this.address.split("/")[0],cityName:this.address.split("/")[1],districtName:this.address.split("/")[2]});this.showDel?(0,r.editAddressBook)(t).then((function(t){1===t.code&&e.redirectTo({url:"/pages/address/address"})})):(delete t.id,(0,r.addAddressBook)(t).then((function(t){1===t.code&&e.redirectTo({url:"/pages/address/address"})})))},deleteAddressFun:function(){var t=this;(0,r.delAddressBook)(this.delId).then((function(n){1===n.code&&(e.redirectTo({url:"/pages/address/address"}),e.showToast({title:"地址删除成功",duration:1e3,icon:"none"}),t.form.name="",t.form.phone="",t.form.address="",t.form.type=1,t.form.radio=0,t.form.provinceCode="11",t.form.cityCode="1101",t.form.districtCode="110102")}))},getTextOption:function(e){this.form.type=e.type}}};t.default=d}).call(this,n("df3c")["default"])},ec63:function(e,t,n){"use strict";var o=n("35ee"),i=n.n(o);i.a},fac4:function(e,t,n){"use strict";n.r(t);var o=n("3142"),i=n("2537");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("ec63");var a=n("828b"),s=Object(a["a"])(i["default"],o["b"],o["c"],!1,null,"7afab60c",null,!1,o["a"],void 0);t["default"]=s.exports}},[["4ad7","common/runtime","common/vendor"]]]);