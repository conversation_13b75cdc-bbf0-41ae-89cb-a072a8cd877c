<block wx:if="{{dishDetailes.type==1}}"><view class="dish_detail_pop data-v-662c4202"><image class="div_big_image data-v-662c4202" mode="aspectFill" src="{{dishDetailes.image}}"></image><view class="title data-v-662c4202">{{dishDetailes.name}}</view><view class="desc data-v-662c4202">{{dishDetailes.description}}</view><view class="but_item data-v-662c4202"><view class="price data-v-662c4202"><text class="ico data-v-662c4202">￥</text>{{''+$root.g0+''}}</view><block wx:if="{{$root.g1}}"><view class="active data-v-662c4202"><image class="dish_red data-v-662c4202" src="../../../static/btn_red.png" mode data-event-opts="{{[['tap',[['redDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image><text class="dish_number data-v-662c4202">{{dishDetailes.dishNumber}}</text><image class="dish_add data-v-662c4202" src="../../../static/btn_add.png" mode data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{$root.g2>0}}"><view class="active data-v-662c4202"><view data-event-opts="{{[['tap',[['moreNormDataesHandle',['$0'],['dishDetailes']]]]]}}" class="dish_card_add data-v-662c4202" bindtap="__e">选择规格</view></view></block><block wx:if="{{$root.g3}}"><view class="active data-v-662c4202"><view data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" class="dish_card_add data-v-662c4202" bindtap="__e">加入购物车</view></view></block></view><view data-event-opts="{{[['tap',[['dishClose',['$event']]]]]}}" class="close data-v-662c4202" bindtap="__e"><image class="close_img data-v-662c4202" src="../../../static/but_close.png" mode></image></view></view></block><block wx:else><view class="dish_detail_pop data-v-662c4202"><scroll-view class="dish_items data-v-662c4202" scroll-y="true" scroll-top="0rpx"><block wx:for="{{dishMealData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="dish_item data-v-662c4202"><image class="div_big_image data-v-662c4202" src="{{item.image}}" mode></image><view class="title data-v-662c4202">{{''+item.name+''}}<text class="data-v-662c4202">{{"X"+item.copies}}</text></view><view class="desc data-v-662c4202">{{item.description}}</view></view></block></scroll-view><view class="but_item data-v-662c4202"><view class="price data-v-662c4202"><text class="ico data-v-662c4202">￥</text>{{''+dishDetailes.price+''}}</view><block wx:if="{{dishDetailes.dishNumber&&dishDetailes.dishNumber>0}}"><view class="active data-v-662c4202"><image class="dish_red data-v-662c4202" src="../../../static/btn_red.png" mode data-event-opts="{{[['tap',[['redDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image><text class="dish_number data-v-662c4202">{{dishDetailes.dishNumber}}</text><image class="dish_add data-v-662c4202" src="../../../static/btn_add.png" mode data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image></view></block><block wx:else><block wx:if="{{dishDetailes.dishNumber==0}}"><view class="active data-v-662c4202"><view data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" class="dish_card_add data-v-662c4202" bindtap="__e">加入购物车</view></view></block></block></view><view data-event-opts="{{[['tap',[['dishClose',['$event']]]]]}}" class="close data-v-662c4202" bindtap="__e"><image class="close_img data-v-662c4202" src="../../../static/but_close.png" mode></image></view></view></block>