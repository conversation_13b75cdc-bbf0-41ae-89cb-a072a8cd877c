{"version": 3, "sources": ["webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/my/components/orderList.vue?8681", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/my/components/orderList.vue?99ed", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/my/components/orderList.vue?0963", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/my/components/orderList.vue?798a", "uni-app:///pages/my/components/orderList.vue"], "names": ["props", "scrollH", "type", "default", "loading", "loadingText", "recentOrdersList", "components", "ReachBottom", "methods", "lower", "goDetail", "numes", "list", "count", "total", "oneOrderFun", "getOvertime", "statusWord", "status", "time"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;;;AAGxD;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAAiyB,CAAgB,mxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACkDrzB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,gBACA;EACA;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;QAAA;MAAA;IACA;EACA;EACAI;IACAC;EACA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACAC,mBACAA;QACAC;QACAC;MACA;MACA;QAAAD;QAAAC;MAAA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QAAAC;QAAAC;MAAA;MACA;IACA;EACA;AACA;AAAA,4B", "file": "pages/my/components/orderList.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./orderList.vue?vue&type=template&id=a830bf26&\"\nvar renderjs\nimport script from \"./orderList.vue?vue&type=script&lang=js&\"\nexport * from \"./orderList.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/components/orderList.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=template&id=a830bf26&\"", "var components\ntry {\n  components = {\n    reachBottom: function () {\n      return import(\n        /* webpackChunkName: \"components/reach-bottom/reach-bottom\" */ \"@/components/reach-bottom/reach-bottom.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.recentOrdersList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.statusWord(item.status)\n    var g0 = item.amount.toFixed(2)\n    var m1 = _vm.numes(item.orderDetailList)\n    var m2 = item.status === 1 && _vm.getOvertime(item.orderTime) > 0\n    return {\n      $orig: $orig,\n      m0: m0,\n      g0: g0,\n      m1: m1,\n      m2: m2,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=script&lang=js&\"", "<!--最近订单-->\n<template>\n  <scroll-view scroll-y=\"true\" :style=\"{ height: scrollH + 'px' }\" @scrolltolower=\"lower\">\n    <view class=\"main recent_orders\">\n      <!-- 最近订单列表 -->\n      <view class=\"box order_lists\" v-for=\"(item, index) in recentOrdersList\" :key=\"index\">\n        <!-- 时间和支付状态 -->\n        <view class=\"date_type\">\n          <!-- 时间 -->\n          <text class=\"time\">{{ item.orderTime }} {{ item.id }}</text>\n          <!-- 支付状态 -->\n          <text class=\"type status\" :class=\"{ status: item.status == 2 }\">{{\n            statusWord(item.status)\n          }}</text>\n        </view>\n        <!-- 点菜的内容 -->\n        <view class=\"orderBox\" @click=\"goDetail(item.id)\">\n          <view class=\"food_num\">\n            <scroll-view scroll-x=\"true\" class=\"pic\" style=\"width: 100%; overflow: hidden; white-space: nowrap\">\n              <view class=\"food_num_item\" v-for=\"(num, y) in item.orderDetailList\" :key=\"y\">\n                <view class=\"img\">\n                  <image :src=\"num.image\"></image>\n                  <!-- <image src=\"../../static/img2.jpg\"></image> -->\n                </view>\n                <view class=\"food\">{{ num.name }}</view>\n              </view>\n            </scroll-view>\n          </view>\n          <view class=\"numAndAum\">\n            <view><text>￥{{ item.amount.toFixed(2) }}</text></view>\n            <view><text>共{{ numes(item.orderDetailList).count }}件</text></view>\n          </view>\n        </view>\n\n        <view class=\"againBtn\">\n          <button class=\"new_btn\" type=\"default\" @click=\"oneOrderFun(item.id)\">\n            再来一单\n          </button>\n          <button class=\"new_btn btn\" type=\"default\" @click=\"goDetail(item.id)\"\n            v-if=\"item.status === 1 && getOvertime(item.orderTime) > 0\">\n            去支付\n          </button>\n        </view>\n      </view>\n    </view>\n    <reach-bottom v-if=\"loading\" :loadingText=\"loadingText\"></reach-bottom>\n  </scroll-view>\n</template>\n<script>\nimport ReachBottom from \"@/components/reach-bottom/reach-bottom.vue\";\nimport { statusWord } from \"@/utils/index\";\nexport default {\n  // 获取父级传的数据\n  props: {\n    // 头像\n    scrollH: {\n      type: Number,\n      default: 0,\n    },\n    //\n    loading: {\n      type: Boolean,\n      default: false,\n    },\n    loadingText: {\n      type: String,\n      default: \"\",\n    },\n    // 例表数据\n    recentOrdersList: {\n      type: Array,\n      default: () => [],\n    },\n  },\n  components: {\n    ReachBottom,\n  },\n  methods: {\n    lower() {\n      this.$emit(\"lower\");\n    },\n    //订单详情\n    goDetail(id) {\n      this.$emit(\"goDetail\", id);\n    },\n    //  1待付款 2待接单 3 已接单 4 派送中 5 已完成 6 已取消 7 退款 \n    numes(list) {\n      let count = 0;\n      let total = 0;\n      list.length > 0 &&\n        list.forEach((obj) => {\n          count += Number(obj.number);\n          total += Number(obj.number) * Number(obj.amount);\n        });\n      return { count: count, total: total };\n    },\n    // 再来一单\n    oneOrderFun(id) {\n      this.$emit(\"oneOrderFun\", id);\n    },\n    //\n    getOvertime(time) {\n      this.$emit(\"getOvertime\", time);\n    },\n    // 支付状态\n    statusWord(status, time) {\n      this.$emit(\"statusWord\", { status: status, time: time });\n      return statusWord(status, time);\n    },\n  },\n};\n</script>"], "sourceRoot": ""}