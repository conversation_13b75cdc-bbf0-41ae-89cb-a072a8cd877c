import navBar from "../common/Navbar/navbar.vue" //标题
import Phone from "@/components/uni-phone/index.vue" //拨打电话
import popMask from "./components/popMask.vue" //规格
import popCart from "./components/popCart.vue" //购物车弹出层
import dishDetail from "./components/dishDetail.vue" //菜品详情
import midManager from "../../utils/midManager.js" //mid参数管理工具
import {
	// 苍穹外卖相关的接口
	userLogin,
	getCategoryList,
	dishListByCategoryId,
	// 查询套餐列表的接口
	querySetmeaList,
	// 获取购物车集合
	getShoppingCartList,
	// 新的购物车添加逻辑接口
	newAddShoppingCartAdd,
	// 新的购物车减少接口
	newShoppingCartSub,
	// 清空购物车
	delShoppingCart,
	// 此接口为首页查询套餐详情展示的接口
	querySetmealDishById,
	// 获取店铺信息
	getShopStatus,
} from "../api/api.js"
import { mapState, mapMutations } from "vuex"
import { baseUrl } from "../../utils/env"
export default {
	data() {
		return {
			title: "Hello",
			// 去结算部分
			openOrderCartList: false,
			// 存放左侧滚动区域菜品分类数组
			typeListData: [],
			dishListData: [],
			// 存放右侧对应菜品每个菜名称的数组
			dishListItems: [],
			dishDetailes: {},
			openDetailPop: false,
			openMoreNormPop: false,
			moreNormDataes: null,
			tableInfo: null,
			moreNormDishdata: null,
			moreNormdata: null,
			// 套餐中查询到的菜品名称
			dishMealData: null,
			openTablePeoPleNumber: 1,
			orderData: 0,
			// 选中左侧菜品的索引
			typeIndex: 0,
			// 控制菜品详情显示
			openTablePop: false,
			// 规格有关的数组
			flavorDataes: [],
			// 加入购物车数量
			orderDishNumber: 0,
			// 菜品金额
			orderDishPrice: 0,
			params: {
				shopId: "f3deb",
				storeId: "1282344676983062530",
				tableId: "1282346960773238786",
			},
			// 添加一个右侧number更新以后重新刷新接口的id --- 这个id来自左侧菜品分类的id
			rightIdAndType: {},
			phoneData: "************", // 默认客服电话
			tablewareNumber: 0,
			shopStatus: null,
			scrollTop: 0,
			menuHeight: 0, // 左边菜单的高度
			menuItemHeight: 0, // 左边菜单item的高度
			itemId: "", // 栏目右边scroll-view用于滚动的id
			arr: [],
			// 添加mid参数用于过滤数据
			mid: null,
			// 不同mid对应的店铺信息配置
			shopConfigs: {
				'1': {
					shopName: '瑞吉外卖总店',
					shopAddress: '北京市海淀区中关村大街1号',
					phoneNumber: '************'
				},
				'2': {
					shopName: '测试店铺1',
					shopAddress: '测试地址1',
					phoneNumber: '18631449103'
				},
				'3': {
					shopName: '测试店铺2',
					shopAddress: '测试地址2',
					phoneNumber: '18631449103'
				},
				'4': {
					shopName: '测试店铺3',
					shopAddress: '测试地址3',
					phoneNumber: '18631449103'
				},
				'default': {
					shopName: '测试店铺4',
					shopAddress: '测试地址4',
					phoneNumber: '18631449103'
				}
			},
		}
	},
	//   组件
	components: {
		navBar,
		Phone,
		popMask,
		popCart,
		dishDetail,
	},


	//   计算属性
	computed: {
		// 购物车信息列表
		orderListDataes: function () {
			return this.orderListData()
		},
		loaddingSt: function () {
			return this.lodding()
		},
		// 计算购物车清单
		orderAndUserInfo: function () {
			let orderData = []
			Array.isArray(this.orderListDataes) &&
				this.orderListDataes.forEach((n, i) => {
					let userData = {}
					userData.nickName = n.name ?? ""
					userData.avatarUrl = n.image ?? ""
					userData.dishList = [n]
					const num = orderData.findIndex(
						(o) => o.nickName == userData.nickName
					)
					if (num != -1) {
						orderData[num].dishList.push(n)
					} else {
						orderData.push(userData)
					}
				})
			return orderData
		},
		ht: function () {
			return (
				uni.getMenuButtonBoundingClientRect().top +
				uni.getMenuButtonBoundingClientRect().height +
				7
			)
		},
		// 根据mid参数获取当前店铺信息
		currentShopInfo: function () {
			// 当mid为空时，默认使用mid=2的配置
			const mid = this.mid || '2'
			return this.shopConfigs[mid] || this.shopConfigs['2']
		},
	},

	onReady() {
		this.getMenuItemTop()
	},
	onLoad(options) {
		uni.onNetworkStatusChange(function (res) {
			if (res.isConnected == false) {
				uni.navigateTo({
					url: "/pages/nonet/index",
				})
			}
		})
		if (options) {
			// 获取mid参数并持久化保存
			if (options.mid) {
				this.mid = options.mid
				midManager.setMid(options.mid)
				console.log('onLoad: 新的mid参数已保存:', options.mid)
			} else {
				// 如果没有传入mid参数，尝试从本地存储获取
				const savedMid = midManager.getMid()
				if (savedMid) {
					this.mid = savedMid
					console.log('onLoad: 使用本地存储的mid参数:', savedMid)
				} else {
					// 如果本地也没有保存mid，默认设置为'2'
					this.mid = '2'
					midManager.setMid('2')
					console.log('onLoad: 设置默认mid参数为: 2')
				}
			}

			// 更新店铺信息
			this.updateShopInfoToStore()

			if (!options.status && !options.formOrder) {
				this.getData()
			}
		}
	},
	onShow() {
		// 每次显示页面时都重新检查本地存储的mid参数，确保使用最新的参数
		const savedMid = midManager.getMid()
		if (savedMid && savedMid !== this.mid) {
			this.mid = savedMid
			console.log('onShow: 更新mid参数为:', savedMid)
			// 更新店铺信息
			this.updateShopInfoToStore()
			// 如果mid参数发生变化，需要重新初始化数据
			if (this.token()) {
				this.init()
			}
			return
		}

		// 如果没有mid参数，设置默认值为'2'
		if (!this.mid) {
			this.mid = '2'
			midManager.setMid('2')
			console.log('onShow: 设置默认mid参数为: 2')
		}

		// 确保店铺信息是最新的
		this.updateShopInfoToStore()

		if (this.token()) {
			this.init()
		}
	},
	methods: {
		//   vuex储存信息
		...mapMutations([
			"setShopInfo", //设置店铺信息
			"setShopStatus", //设置店铺状态
			"initdishListMut", //设置购物车订单
			"setStoreInfo",
			"setBaseUserInfo", //设置用户基本信息
			"setLodding",
			"setToken", //设置token
			"setDeliveryFee", //设置配送费
		]),
		// 从vuex信息
		...mapState([
			"shopInfo", //店铺信息
			"orderListData",
			"baseUserInfo", //用户信息
			"lodding",
			"token", //token
			"deliveryFee", //配送费
		]),

		// 安全获取配送费，确保返回有效数字
		deliveryFee() {
			const fee = this.$store.state.deliveryFee
			if (fee === null || fee === undefined || isNaN(fee)) {
				return 0
			}
			return Number(fee)
		},
		loginSync() {
			return new Promise((resolve, reject) => {
				uni.login({
					success: (loginRes) => {
						if (loginRes.errMsg === "login:ok") {
							resolve(loginRes.code)
						}
					},
				})
			})
		},
		// 获取用户信息
		getData() {
			let res = wx.getMenuButtonBoundingClientRect()
			let _this = this
			// 获取店铺状态
			this.getShopInfo()
			this.selectHeight = res.height
			if (this.token() === "") {
				uni.showModal({
					title: "温馨提示",
					content: "亲，授权微信登录后才能点单！",
					showCancel: false,
					success(res) {
						if (res.confirm) {
							let jsCode = ""
							uni.login({
								provider: "weixin",
								success: (loginRes) => {
									if (loginRes.errMsg === "login:ok") {
										jsCode = loginRes.code
									}
								},
							})
							// 授权
							uni.getUserProfile({
								desc: "登录",
								success: function (userInfo) {
									// 显示加载动画
									_this.setLodding(true)

									_this.setBaseUserInfo(userInfo.userInfo)
									const params = {
										code: jsCode,
									}

									// 直接调用登录接口，不获取地理位置
									userLogin(params)
										.then((success) => {
											if (success.code === 1) {
												_this.setToken(success.data.token)
												// 保存配送费
												_this.setDeliveryFee(success.data.deliveryFee)
												// 保存商铺信息
												_this.setShopInfo({
													shopName: success.data.shopName,
													shopAddress: success.data.shopAddress,
													shopId: success.data.shopId,
												})
												_this.init()
											}
										})
										.catch((err) => {
											_this.setLodding(false)
											uni.showToast({
												title: "登录失败",
												icon: "none"
											})
										})
								},
								fail: function (err) {
									_this.setLodding(false)
								},
							})
						}
					},
				})
			}
		},

		async init() {
			// 显示加载动画
			this.setLodding(true)

			// 获取菜品和套餐分类接口
			if (this.typeIndex !== 0) {
				this.typeIndex = 0
			}

			console.log('init: 当前使用的mid参数:', this.mid)

			try {
				const res = await getCategoryList()
				if (res && res.code === 1) {
					let filteredData = res.data
					// 如果有mid参数，则过滤数据，只显示createUser与mid匹配的数据
					if (this.mid) {
						console.log('init: 根据mid参数过滤数据:', this.mid)
						filteredData = res.data.filter(item => {
							return item.createUser && item.createUser.toString() === this.mid.toString()
						})
						console.log('init: 过滤后的数据数量:', filteredData.length)
					}
					this.typeListData = [...filteredData]
					if (filteredData.length > 0) {
						await this.getDishListDataes(filteredData[this.typeIndex || 0])
					}
				}

				// 调用一次购物车集合---初始化
				await this.getTableOrderDishListes()

			} catch (error) {
				console.error('init: 数据加载失败:', error)
				uni.showToast({
					title: '数据加载失败',
					icon: 'none'
				})
			} finally {
				// 隐藏加载动画
				this.setLodding(false)
			}
		},
		// 点击左边的栏目切换
		async swichMenu(params, index) {
			if (this.arr.length == 0) {
				await this.getMenuItemTop()
			}
			if (index == this.typeIndex) return

			this.$nextTick(function () {
				this.typeIndex = index
				this.leftMenuStatus(index)
			})

			// 切换菜单时获取对应的菜品数据
			await this.getDishListDataes(params, index)
		},
		// 获取一个目标元素的高度
		getElRect(elClass, dataVal) {
			new Promise((resolve, reject) => {
				const query = uni.createSelectorQuery().in(this)
				query
					.select("." + elClass)
					.fields(
						{
							size: true,
						},
						(res) => {
							// 如果节点尚未生成，res值为null，循环调用执行
							if (!res) {
								setTimeout(() => {
									this.getElRect(elClass)
								}, 10)
								return
							}
							this[dataVal] = res.height
							resolve()
						}
					)
					.exec()
			})
		},
		// 设置左边菜单的滚动状态
		async leftMenuStatus(index) {
			this.typeIndex = index
			// 如果为0，意味着尚未初始化
			if (this.menuHeight == 0 || this.menuItemHeight == 0) {
				await this.getElRect("menu-scroll-view", "menuHeight")
				await this.getElRect("type_item", "menuItemHeight")
			}
			// 将菜单活动item垂直居中
			this.scrollTop =
				index * this.menuItemHeight +
				this.menuItemHeight / 2 -
				this.menuHeight / 2
		},
		// 获取右边菜单每个item到顶部的距离
		getMenuItemTop() {
			new Promise((resolve) => {
				let selectorQuery = uni.createSelectorQuery()
				selectorQuery
					.selectAll(".class-item")
					.boundingClientRect((rects) => {
						// 如果节点尚未生成，rects值为[](因为用selectAll，所以返回的是数组)，循环调用执行
						if (!rects.length) {
							setTimeout(() => {
								this.getMenuItemTop()
							}, 10)
							return
						}
					})
					.exec()
			})
		},
		// 获取菜品列表
		async getDishListDataes(params, index) {
			// 显示加载动画
			this.setLodding(true)

			this.rightIdAndType = {}
			this.rightIdAndType = {
				id: params.id,
				type: params.type,
			}
			const param = {
				categoryId: params.id,
			}

			try {
				// type：1是菜品、2是套餐
				if (params.type === 1) {
					const res = await dishListByCategoryId(param)
					if (res && res.code === 1) {
						// 添加一个字段去实时更新加入购物车number数量 ----- newCardNumber
						this.dishListData =
							res.data &&
							res.data.map((obj) => ({
								...obj,
								type: 1,
								newCardNumber: 0,
							}))
					}
				} else {
					// 套餐
					const success = await querySetmeaList(param)
					if (success && success.code === 1) {
						// dishListItems被转换数组---原始this.dishListData
						this.dishListData =
							success.data &&
							success.data.map((obj) => ({
								...obj,
								type: 2,
								newCardNumber: 0,
							}))
					}
				}

				this.typeIndex = index
				this.setOrderNum()

			} catch (error) {
				console.error('getDishListDataes: 菜品数据加载失败:', error)
				uni.showToast({
					title: '菜品数据加载失败',
					icon: 'none'
				})
			} finally {
				// 隐藏加载动画
				this.setLodding(false)
			}
		},
		// 获取首页店铺信息
		async getShopInfo() {
			try {
				const res = await getShopStatus()
				this.shopStatus = res.data
				console.log(res.data);
				this.setShopStatus(res.data)
			} catch (error) {
				console.error('getShopInfo: 店铺信息获取失败:', error)
				uni.showToast({
					title: '店铺信息获取失败',
					icon: 'none'
				})
			}
		},

		// 重新拼装image
		getNewImage(image) {
			return `${baseUrl}/common/download?name=${image}`
		},
		// 获取购物车订单列表
		async getTableOrderDishListes() {
			try {
				// 调用获取购物车集合接口
				const res = await getShoppingCartList({})
				if (res.code === 1) {
					this.initdishListMut(res.data)
					this.computOrderInfo()
				}
			} catch (error) {
				console.error('getTableOrderDishListes: 购物车数据加载失败:', error)
			}
		},
		// 去订单页面
		goOrder() {
			uni.navigateTo({
				url: "/pages/order/index",
			})
		},
		// 加菜 - 添加菜品
		async addDishAction(item, form) {
			// 处理从dishDetail组件传来的参数格式
			if (item && typeof item === 'object' && item.obj && item.item) {
				// 来自dishDetail组件的参数格式: {obj: dishData, item: formType}
				const actualItem = item.obj
				const actualForm = item.item
				return this.addDishAction(actualItem, actualForm)
			}
			// 规格
			if (
				this.openMoreNormPop &&
				(!this.flavorDataes || this.flavorDataes.length <= 0)
			) {
				uni.showToast({
					title: "请选择规格",
					icon: "none",
				})
				return false
			}

			// 显示加载动画
			this.setLodding(true)

			this.openMoreNormPop = false
			// 实时更新obj.newCardNumber新添加的字段----加入购物车数量number
			this.tablewareNumber++
			this.dishDetailes.dishNumber++
			if (
				this.orderListDataes &&
				!this.orderListDataes.some((n) => n.id == item.dishId) &&
				this.flavorDataes.length > 0
			) {
				item.flavorRemark = JSON.stringify(this.flavorDataes)
			}
			// 有sort字段是菜品
			let dishFlavorDatas = ""
			let flavorRemark = []
			if (item.flavorRemark) {
				flavorRemark = JSON.parse(item.flavorRemark)
			}
			if (item.dishFlavor !== "" && item.dishFlavor) {
				dishFlavorDatas = item.dishFlavor
			} else if (flavorRemark.length > 0) {
				dishFlavorDatas = flavorRemark.join(',')
			} else {
				dishFlavorDatas = null
			}
			let params = {
				dishFlavor: dishFlavorDatas,
			}
			if (item.type === 1) {
				params = {
					...params,
					dishId: item.id,
				}
			} else if (item.type === 2) {
				params = {
					setmealId: item.id,
				}
			} else if (form === "购物车") {
				if (item.dishId) {
					params = {
						...params,
						dishId: item.dishId,
					}
				} else {
					params = {
						setmealId: item.setmealId,
					}
				}
			}

			try {
				const res = await newAddShoppingCartAdd(params)
				if (res.code === 1) {
					// 调用一次购物车集合---初始化
					await this.getTableOrderDishListes()
					// 重新调取刷新右侧具体菜品列表
					await this.getDishListDataes(this.rightIdAndType)
					this.flavorDataes = []
				}
			} catch (error) {
				console.error('addDishAction: 添加菜品失败:', error)
				uni.showToast({
					title: '添加菜品失败',
					icon: 'none'
				})
			} finally {
				// 隐藏加载动画
				this.setLodding(false)
			}
		},
		// 加入购物车
		addShop(item) {
			console.log(item);
			this.dishDetailes = item
			this.addDishAction(item, "普通")
		},
		// 减菜 - 添加菜品
		async redDishAction(item, form) {
			// 处理从dishDetail组件传来的参数格式
			if (item && typeof item === 'object' && item.obj && item.item) {
				// 来自dishDetail组件的参数格式: {obj: dishData, item: formType}
				const actualItem = item.obj
				const actualForm = item.item
				return this.redDishAction(actualItem, actualForm)
			}
			// 显示加载动画
			this.setLodding(true)

			// 实时更新obj.newCardNumber新添加的字段----加入购物车数量number
			this.tablewareNumber--
			this.dishDetailes.dishNumber--
			let dishFlavorDatas = ""
			let flavorRemark = []
			if (item.flavorRemark) {
				flavorRemark = JSON.parse(item.flavorRemark)
			}
			if (item.dishFlavor !== "" && item.dishFlavor) {
				dishFlavorDatas = item.dishFlavor
			} else if (flavorRemark.length > 0) {
				dishFlavorDatas = flavorRemark[0]
			} else {
				dishFlavorDatas = null
			}
			let params = {
				dishFlavor: dishFlavorDatas,
			}
			if (item.type === 1) {
				params = {
					...params,
					dishId: item.id,
				}
			} else if (item.type === 2) {
				params = {
					// ...params,
					setmealId: item.id,
				}
			} else if (form === "购物车") {
				if (item.dishId) {
					params = {
						...params,
						dishId: item.dishId,
					}
				} else {
					params = {
						setmealId: item.setmealId,
					}
				}
			}

			try {
				const res = await newShoppingCartSub(params)
				if (res.code === 1) {
					// 调用一次购物车集合---初始化
					await this.getTableOrderDishListes()
					// 重新调取刷新右侧具体菜品列表
					await this.getDishListDataes(this.rightIdAndType)
				}
			} catch (error) {
				console.error('redDishAction: 减少菜品失败:', error)
				uni.showToast({
					title: '减少菜品失败',
					icon: 'none'
				})
			} finally {
				// 隐藏加载动画
				this.setLodding(false)
			}
		},
		// 清空购物车
		async clearCardOrder() {
			// 显示加载动画
			this.setLodding(true)

			try {
				await delShoppingCart()
				this.openOrderCartList = false
				// 调用一次购物车集合---初始化
				await this.getTableOrderDishListes()
				// 重新调取刷新右侧具体菜品列表
				await this.getDishListDataes(this.rightIdAndType)
			} catch (error) {
				console.error('clearCardOrder: 清空购物车失败:', error)
				uni.showToast({
					title: '清空购物车失败',
					icon: 'none'
				})
			} finally {
				// 隐藏加载动画
				this.setLodding(false)
			}
		},
		// 打开菜品牌详情
		async openDetailHandle(item) {
			this.dishDetailes = item
			if (item.type === 2) {
				// 显示加载动画
				this.setLodding(true)

				try {
					const res = await querySetmealDishById({
						id: item.id,
					})
					if (res.code === 1) {
						this.openDetailPop = true
						this.dishMealData = res.data
					}
				} catch (error) {
					console.error('openDetailHandle: 套餐详情加载失败:', error)
					uni.showToast({
						title: '套餐详情加载失败',
						icon: 'none'
					})
				} finally {
					// 隐藏加载动画
					this.setLodding(false)
				}
			} else {
				this.openDetailPop = true
			}
		},
		// 关闭菜品详情
		dishClose() {
			this.openDetailPop = false
		},
		// 多规格数据处理
		moreNormDataesHandle(item) {
			this.flavorDataes.splice(0)
			this.moreNormDishdata = item
			this.openDetailPop = false
			this.openMoreNormPop = true
			this.moreNormdata = item.flavors.map((obj) => ({
				...obj,
				value: JSON.parse(obj.value),
			}))
			this.moreNormdata.forEach((item) => {
				if (item.value && item.value.length > 0) {
					this.flavorDataes.push(item.value[0])
				}
			})
		},
		// 选规格 处理一行只能选择一种
		checkMoreNormPop(val) {
			let obj = val.obj
			let item = val.item
			let ind
			let findst = obj.some((n) => {
				ind = this.flavorDataes.findIndex((o) => o == n)
				return ind != -1
			})
			const num = this.flavorDataes.findIndex((it) => it == item)
			if (num == -1 && !findst) {
				this.flavorDataes.push(item)
			} else if (findst) {
				this.flavorDataes.splice(ind, 1)
				this.flavorDataes.push(item)
			} else {
				this.flavorDataes.splice(num, 1)
			}
		},
		// 关闭选规格弹窗
		closeMoreNorm(moreNormDishdata) {
			this.flavorDataes.splice(0, this.flavorDataes.length)
			this.openMoreNormPop = false
		},
		// 订单里和总订单价格计算
		computOrderInfo() {
			let oriData = this.orderListDataes
			this.orderDishNumber = this.orderDishPrice = 0
			oriData.map((n, i) => {
				this.orderDishNumber += n.number
				this.orderDishPrice += n.number * n.amount
			})
			this.orderDishPrice = this.orderDishPrice
		},
		// 处理点餐数量 - 更新菜品已点餐数量
		setOrderNum() {
			let ODate = this.dishListData
			let CData = this.orderListDataes
			ODate &&
				ODate.map((obj, index) => {
					obj.dishNumber = 0
					// 去除空的规格
					if (obj.flavors) {
						obj.flavors.forEach((value, i) => {
							if (value.name === "") {
								obj.flavors.splice(i, 1)
							}
						})
					}

					if (CData.length > 0) {
						CData &&
							CData.forEach((tg, ind) => {
								if (obj.id === tg.dishId) {
									obj.dishNumber = tg.number
								}
								if (obj.id === tg.setmealId) {
									obj.dishNumber = tg.number
								}
							})
					}
				})
			if (this.dishListItems.length == 0) {
				this.dishListItems = ODate
			} else {
				this.dishListItems.splice(0, this.dishListItems.length, ...ODate)
			}
		},
		// 拨打电话弹层
		handlePhone(type) {
			// 根据当前mid参数设置电话号码
			this.phoneData = this.currentShopInfo.phoneNumber
			this.$refs.phone.$refs.popup.open(type)
		},
		// 关闭电话弹层
		closePopup(type) {
			this.$refs.phone.$refs.popup.close(type)
		},
		disabledScroll() {
			return false
		},
		// 测试加载动画的方法
		testLoading() {
			this.setLodding(true)
			setTimeout(() => {
				this.setLodding(false)
			}, 3000)
		},
		// 更新店铺信息到Vuex
		updateShopInfoToStore() {
			const shopInfo = this.currentShopInfo
			const currentMid = this.mid || '2'  // 默认使用mid=2
			this.setShopInfo({
				shopName: shopInfo.shopName,
				shopAddress: shopInfo.shopAddress,
				shopId: currentMid,
				phoneNumber: shopInfo.phoneNumber
			})
			// 同时更新电话号码
			this.phoneData = shopInfo.phoneNumber
			console.log('店铺信息已更新 (mid=' + currentMid + '):', shopInfo)
		},
		// 测试切换mid的方法（开发调试用）
		testSwitchMid(newMid) {
			this.mid = newMid
			midManager.setMid(newMid)
			console.log('测试切换mid到:', newMid)
		},
	},
}
