<view class="products-list-container data-v-fb16ccc0"><view class="navbar data-v-fb16ccc0"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="nav-back data-v-fb16ccc0" bindtap="__e"><text class="back-icon data-v-fb16ccc0">‹</text></view><view class="nav-title data-v-fb16ccc0">商品列表</view></view><view class="products-grid data-v-fb16ccc0"><block wx:for="{{$root.l0}}" wx:for-item="product" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['goToProduct',['$0'],[[['productList','id',product.$orig.id]]]]]]]}}" class="product-card data-v-fb16ccc0" bindtap="__e"><view class="product-image-wrapper data-v-fb16ccc0"><image class="product-image data-v-fb16ccc0" src="{{product.$orig.image}}" mode="aspectFill"></image></view><view class="product-info data-v-fb16ccc0"><view class="product-name data-v-fb16ccc0">{{product.$orig.name}}</view><view class="product-price data-v-fb16ccc0"><text class="price-symbol data-v-fb16ccc0">¥</text><text class="price-value data-v-fb16ccc0">{{product.g0}}</text></view></view><view class="product-button data-v-fb16ccc0"><button data-event-opts="{{[['tap',[['goToProduct',['$0'],[[['productList','id',product.$orig.id]]]]]]]}}" class="view-button data-v-fb16ccc0" catchtap="__e">查看详情</button></view></view></block></view></view>