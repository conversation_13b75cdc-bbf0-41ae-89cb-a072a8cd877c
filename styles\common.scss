.box {
  border-radius: 8rpx;
  background-color: #fff;
  margin: 0 0 20rpx 0;
  box-sizing: border-box;
  position: relative;
}
.main {
  width: 710rpx;
  margin: 0 auto;
}
.tag {
  width: 68rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  border-radius: 4rpx;
  background: #e1f1fe;
  display: inline-block;
  margin-right: 8rpx;
  color: #333333;
  font-size: 24rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
}
// 订单列表
.recent_orders {
  // width: 710rpx;
  margin: 20rpx auto;

  // 最近订单列表
  .order_lists {
    padding: 28rpx 20rpx 0;
    .date_type {
      opacity: 1;
      font-size: 28rpx;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      height: 40rpx;
      line-height: 40rpx;
      letter-spacing: 0px;
      .time {
        display: inline-block;
        color: #333333;
        // font-weight: 600;
      }
      .type {
        display: inline-block;
        color: #666666;
        float: right;
      }
      .status {
        color: #f58c21;
      }
    }
    .orderBox {
      // display: flex;
      position: relative;
      // overflow: hidden;
    }
    .food_num {
      // margin: 0 30rpx 0 0;
      padding-bottom: 32rpx;
      flex: 1;
      display: flex;
      .food_num_item {
        margin-top: 24rpx;
        margin-right: 14rpx;
        width: 156rpx;
        height: 120rpx;
        display: inline-block;
        // line-height: 40rpx;
        .img {
          image {
            width: 156rpx;
            height: 120rpx;
            border-radius: 8rpx;
            display: block;
          }
        }
      }
      .food {
        height: 40rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-top: 12rpx;
        font-size: 26rpx;
        color: #666;
      }
    }
    .numAndAum {
      height: 160rpx;
      opacity: 1;
      font-size: 26rpx;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      color: #666666;
      line-height: 40rpx;
      text-align: center;
      position: absolute;
      right: 0;
      top: 0;
      background: rgba(255, 255, 255, 0.76);
      padding: 46rpx 10rpx 0;
      view {
        &:first-child {
          text {
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 600;
            color: #333333;
          }
        }
        &:last-child {
          font-size: 24rpx;
          color: #666;
          text {
            padding: 0 10rpx;
          }
        }
      }
    }
  }
}
// 按钮部分
.againBtn {
  // margin: right;
  padding-bottom: 20rpx;
  height: 72rpx;
  text-align: right;
  .new_btn {
    width: 172rpx;
    height: 68rpx;
    line-height: 68rpx;
    border-color: #e5e4e4;
    background-color: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    color: #333333;
    margin-left: 20rpx;
    display: inline-block;
  }
  .btn {
    background: #ffc200;
  }
}
//
.phoneIcon {
  background: url(../image/phone.png);
  background-size: contain;
  width: 42rpx;
  height: 42rpx;
  display: inline-block;
  vertical-align: middle;
}
.container {
  .popup-content {
    height: auto;
    padding: 0 0 0rpx;
    display: block;
  }
  .uni-popup {
    z-index: 9999;
    .popup-content {
      border-radius: 8rpx 8rpx 0 0;
    }
  }
}
.popup-content {
  align-items: center;
  justify-content: center;
  padding: 15px;
  height: 50px;
  background-color: #fff;
}
// 弹窗
.popupBox {
  .popupTitle {
    background: #fef6e9;
    padding: 24rpx 30rpx;
    line-height: 34rpx;
    border-radius: 8rpx 8rpx 0 0;
    text {
      color: #f58c21;
    }
  }
  .popupCon {
    padding: 30rpx 0;
    .popupBtn {
      display: flex;
      padding: 0 68rpx 38rpx;
      border-bottom: 2rpx solid #efefef;
      text {
        flex: 1;
        text-align: center;
        font-size: 28rpx;
        line-height: 40rpx;
        &:first-child {
          text-align: left;
        }
        &:nth-child(2) {
          font-size: 32rpx;
          font-weight: 500;
        }
        &:last-child {
          text-align: right;
        }
      }
    }
    .popupList {
      & > view {
        border-bottom: 2rpx solid #efefef;
        padding: 40rpx 0 27rpx;
        text-align: center;
      }
    }
  }
  .popupSet {
    background: #f6f6f6;
    padding: 30rpx;
    font-size: 28rpx;
    text-align: center;
    margin: 0 32rpx;
    border-radius: 8rpx;
    color: #666;
    view {
      &:last-child {
        padding-top: 34rpx;
        color: #333;
        radio-group {
          display: flex;
          width: 100%;
          label {
            flex: 1;
            display: block;
            radio {
              transform: scale(0.7);
              // color:#F58C21 ;
            }
          }
        }
      }
    }
  }
}
.closePopup {
  background: #fff;
  padding: 40rpx;
  text-align: center;
}
.colseShop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: rgba(0, 0, 0, 0);
  z-index: 9999;
  .shop {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba($color: #000000, $alpha: 0.63);
    color: #fff;
    height: 200rpx;
    line-height: 200rpx;
    text-align: center;

    font-size: 36rpx;
    font-weight: 600;
  }
}
// 拨打电话
.phoneCon {
  .popupBox {
    text-align: center;
    color: #333;
    font-size: 32rpx;
    line-height: 44rpx;
    .popup-content {
      padding: 0;
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      padding-bottom: 20rpx;
      & > view {
        height: 120rpx;
        line-height: 120rpx;
        font-size: 32rpx;
        color: #333;
        &:first-child {
          border-bottom: 2rpx solid #efefef;
          color: #666;
          font-size: 26rpx;
        }
        &:last-child {
          height: 100rpx;
          line-height: 100rpx;
        }
      }
    }
  }

  .closePopup {
    border-top: 12rpx solid #f6f6f6;
    padding-top: 10rpx;
    padding-bottom: 10rpx;
  }
}
.comPopupBox {
  .popup-content {
    border-radius: 8rpx;
    width: 500rpx;
    text-align: center;
    font-size: 28rpx;
    line-height: 40rpx;
    height: auto;
    padding: 0;
    .text {
      padding: 60rpx;
    }
    .btn {
      border-top: 2rpx solid #efefef;
      display: flex;
      font-size: 32rpx;
      & > view {
        padding: 24rpx 0;
        flex: 1;
        &:first-child {
          border-right: 2rpx solid #efefef;
        }
        &:last-child {
          color: #f58c21;
        }
      }
    }
  }
}
// 标签
.tag {
  width: 68rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  border-radius: 4rpx;
  background: #e1f1fe;
  display: inline-block;
  margin-right: 8rpx;
  color: #333333;
  font-size: 24rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
}
.tag2 {
  background: #fef8e7;
}
.tag3 {
  background: #e7fef8;
}
// 单选颜色设置
.payBox {
  .wx-checkbox-input-checked,
  .wx-radio-input-checked,
  .wx-switch-input-checked {
    background-color: #ffc200 !important;
    border-color: #ffc200 !important;
    color: #000 !important;
    transform: scale(0.7);
  }
}
radio .wx-radio-input.wx-radio-input-checked::before {
  border-radius: 50%; /* 圆角 */
  width: 36rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
  height: 36rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
  line-height: 36rpx;
  text-align: center;
  font-size: 40rpx; /* 对勾大小 30rpx */
  font-weight: 600;
  color: #000; /* 对勾颜色 白色 */
  border: 1rpx solid #ffc200;
  background: #ffc200;
  // transform:scale(1);
  // -webkit-transform:translate(-50%, -50%) scale(1);
}
.dish_dishFlavor {
  font-size: 20rpx;
  color: #666;
}
