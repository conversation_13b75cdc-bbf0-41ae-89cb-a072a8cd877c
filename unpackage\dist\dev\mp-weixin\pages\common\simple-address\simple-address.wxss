@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.simple-address.data-v-6d425f7a {
  display: flex;
  flex-direction: column;
}
.simple-address-mask.data-v-6d425f7a {
  position: fixed;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  transition-property: opacity;
  transition-duration: 0.3s;
  opacity: 0;
  z-index: 99;
}
.mask-ani.data-v-6d425f7a {
  transition-property: opacity;
  transition-duration: 0.2s;
}
.simple-bottom-mask.data-v-6d425f7a {
  opacity: 1;
}
.simple-center-mask.data-v-6d425f7a {
  opacity: 1;
}
.simple-address--fixed.data-v-6d425f7a {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  transition-duration: 0.3s;
  -webkit-transform: translateY(460rpx);
          transform: translateY(460rpx);
  z-index: 99;
}
.simple-address-content.data-v-6d425f7a {
  background-color: #FFFFFF;
}
.simple-content-bottom.data-v-6d425f7a {
  bottom: 0;
  left: 0;
  right: 0;
  -webkit-transform: translateY(500rpx);
          transform: translateY(500rpx);
}
.content-ani.data-v-6d425f7a {
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  transition-duration: 0.2s;
}
.simple-bottom-content.data-v-6d425f7a {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.simple-center-content.data-v-6d425f7a {
  -webkit-transform: scale(1);
          transform: scale(1);
  opacity: 1;
}
.simple-address__header.data-v-6d425f7a {
  position: relative;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  border-bottom-color: #f2f2f2;
  border-bottom-style: solid;
  border-bottom-width: 1rpx;
}
.simple-address--fixed-top.data-v-6d425f7a {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-top-color: #c8c7cc;
  border-top-style: solid;
  border-top-width: 1rpx;
}
.simple-address__header-btn-box.data-v-6d425f7a {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 100rpx;
}
.simple-address__header-text.data-v-6d425f7a {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  line-height: 70rpx;
  padding-left: 40rpx;
  padding-right: 40rpx;
}
.simple-address__box.data-v-6d425f7a {
  position: relative;
}
.simple-address-view.data-v-6d425f7a {
  position: relative;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 408rpx;
  background-color: white;
}
.picker-item.data-v-6d425f7a {
  text-align: center;
  line-height: 70rpx;
  text-overflow: ellipsis;
  font-size: 28rpx;
}

