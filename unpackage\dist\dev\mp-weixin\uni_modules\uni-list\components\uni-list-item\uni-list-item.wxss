@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-list-item {
  display: flex;
  font-size: 16px;
  position: relative;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  flex-direction: row;
}
.uni-list-item--disabled {
  opacity: 0.3;
}
.uni-list-item--hover {
  background-color: #f1f1f1 !important;
}
.uni-list-item__container {
  position: relative;
  display: flex;
  flex-direction: row;
  padding: 12px 15px;
  padding-left: 15px;
  flex: 1;
  overflow: hidden;
}
.container--right {
  padding-right: 0;
}
.uni-list--border {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}
.uni-list--border:after {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: #e5e5e5;
}
.uni-list-item__content {
  display: flex;
  padding-right: 8px;
  flex: 1;
  color: #3b4144;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}
.uni-list-item__content--center {
  justify-content: center;
}
.uni-list-item__content-title {
  font-size: 14px;
  color: #3b4144;
  overflow: hidden;
}
.uni-list-item__content-note {
  margin-top: 6rpx;
  color: #999;
  font-size: 12px;
  overflow: hidden;
}
.uni-list-item__extra {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.uni-list-item__header {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uni-list-item__icon {
  margin-right: 18rpx;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.uni-list-item__icon-img {
  display: block;
  height: 26px;
  width: 26px;
  margin-right: 10px;
}
.uni-icon-wrapper {
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.flex--direction {
  flex-direction: column;
  align-items: initial;
}
.flex--justify {
  justify-content: initial;
}
.uni-list--lg {
  height: 40px;
  width: 40px;
}
.uni-list--base {
  height: 26px;
  width: 26px;
}
.uni-list--sm {
  height: 20px;
  width: 20px;
}
.uni-list-item__extra-text {
  color: #999;
  font-size: 12px;
}
.uni-ellipsis-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.uni-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

