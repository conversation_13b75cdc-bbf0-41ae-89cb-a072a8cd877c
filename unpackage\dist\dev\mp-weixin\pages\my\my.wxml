<view class="data-v-0be17cc6"><uni-nav-bar vue-id="704e9d00-1" left-icon="back" leftIcon="arrowleft" title="地址管理" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#ffc200" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-0be17cc6" bind:__l="__l"></uni-nav-bar><view class="my-center data-v-0be17cc6"><view psersonUrl="{{psersonUrl}}" nickName="{{nickName}}" gender="{{gender}}" phoneNumber="{{phoneNumber}}" getPhoneNum="{{getPhoneNum}}" class="_head data-v-0be17cc6"></view><view class="container data-v-0be17cc6"><order-info bind:goAddress="__e" bind:goOrder="__e" vue-id="704e9d00-2" data-event-opts="{{[['^goAddress',[['goAddress']]],['^goOrder',[['goOrder']]]]}}" class="data-v-0be17cc6" bind:__l="__l"></order-info><block wx:if="{{$root.g0}}"><view class="recent data-v-0be17cc6"><text class="order_line data-v-0be17cc6">最近订单</text></view></block><order-list vue-id="704e9d00-3" scrollH="{{scrollH}}" loading="{{loading}}" loadingText="{{loadingText}}" recentOrdersList="{{recentOrdersList}}" data-event-opts="{{[['^lower',[['lower']]],['^goDetail',[['goDetail']]],['^oneOrderFun',[['oneOrderFun']]],['^getOvertime',[['getOvertime']]],['^statusWord',[['statusWord']]],['^numes',[['numes']]]]}}" bind:lower="__e" bind:goDetail="__e" bind:oneOrderFun="__e" bind:getOvertime="__e" bind:statusWord="__e" bind:numes="__e" class="data-v-0be17cc6" bind:__l="__l"></order-list></view></view></view>