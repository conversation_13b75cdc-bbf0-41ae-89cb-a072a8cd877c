(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/details/components/deliveryInfo"],{"0669":function(t,e,n){"use strict";n.r(e);var a=n("aed5"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=r.a},"0a54":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},r=[]},"66c8":function(t,e,n){"use strict";n.r(e);var a=n("0a54"),r=n("0669");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);n("3ed0");var o=n("828b"),u=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=u.exports},aed5:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{orderDetailsData:{type:Object,default:function(){return{}}}},computed:{cryptoName:function(){return this.orderDetailsData.consignee?0==this.orderDetailsData.sex?this.orderDetailsData.consignee.charAt(0)+"先生":this.orderDetailsData.consignee.charAt(0)+"女士":""}}};e.default=a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages/details/components/deliveryInfo-create-component',
    {
        'pages/details/components/deliveryInfo-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("66c8"))
        })
    },
    [['pages/details/components/deliveryInfo-create-component']]
]);
