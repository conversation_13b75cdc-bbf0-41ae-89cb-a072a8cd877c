<view class="customer-box data-v-745a5bfa"><uni-nav-bar vue-id="d05318ec-1" left-icon="back" leftIcon="arrowleft" title="订单备注" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-745a5bfa" bind:__l="__l"></uni-nav-bar><view class="wrap data-v-745a5bfa"><view class="box data-v-745a5bfa"><view class="contion data-v-745a5bfa"><view class="order_list data-v-745a5bfa"><view class="uni-textarea data-v-745a5bfa"><textarea class="{{['beizhu_text','data-v-745a5bfa',(platform==='ios')?'beizhu_text_ios':'']}}" placeholder-class="textarea-placeholder" placeholder="无接触配送，将商品挂家门口或放前台，地址封闭管理时请电话联系" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]]]]]}}" value="{{remark}}" bindinput="__e">{{getVal}}</textarea><text class="numText data-v-745a5bfa"><text class="{{['data-v-745a5bfa',numVal===0?'tip':'']}}">{{numVal}}</text>/50</text></view></view></view></view><view class="btnBox data-v-745a5bfa"><button class="add_btn data-v-745a5bfa" type="primary" plain="true" data-event-opts="{{[['tap',[['handleSaveRemark',['$event']]]]]}}" bindtap="__e">完成</button></view></view></view>