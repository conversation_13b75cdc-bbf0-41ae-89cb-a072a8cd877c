{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/remark/index.vue?7bce", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/remark/index.vue?f869", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/remark/index.vue?58de", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/remark/index.vue?ebad", "uni-app:///pages/remark/index.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "remark", "numVal", "computed", "getVal", "onLoad", "console", "methods", "goBack", "uni", "url", "handleSaveRemark", "validateTextLength", "length"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACyD;AACjC;;;AAG5E;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,+wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACgDjzB;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACAC;IACA;MACA;IACA;MACA;MACA;IACA;EACA;EACAC,uDACA,yCACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACAF;QACAC;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;QACAC;QACA;MACA;QACA;MACA;IACA;EAAA;AAEA;AAAA,2B", "file": "pages/remark/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/remark/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=27c0a608&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./../common/Navbar/navbar.scss?vue&type=style&index=0&id=27c0a608&lang=scss&scoped=true&\"\nimport style1 from \"./../order/style.scss?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"27c0a608\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/remark/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=27c0a608&scoped=true&\"", "var components\ntry {\n  components = {\n    uniNavBar: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-nav-bar/uni-nav-bar\" */ \"@/components/uni-nav-bar/uni-nav-bar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"customer-box\">\n    <uni-nav-bar\n      @clickLeft=\"goBack\"\n      left-icon=\"back\"\n      leftIcon=\"arrowleft\"\n      title=\"订单备注\"\n      statusBar=\"true\"\n      fixed=\"true\"\n      color=\"#ffffff\"\n      backgroundColor=\"#333333\"\n    ></uni-nav-bar>\n    <view class=\"wrap\">\n      <view class=\"box\">\n        <view class=\"contion\">\n          <view class=\"order_list\">\n            <view class=\"uni-textarea\">\n              <textarea\n                class=\"beizhu_text\"\n                :class=\"{ beizhu_text_ios: platform === 'ios' }\"\n                placeholder-class=\"textarea-placeholder\"\n                v-model=\"remark\"\n                placeholder=\"无接触配送，将商品挂家门口或放前台，地址封闭管理时请电话联系\"\n                >{{ getVal }}</textarea\n              >\n              <text class=\"numText\"\n                ><text :class=\"numVal === 0 ? 'tip' : ''\">{{ numVal }}</text\n                >/50</text\n              >\n            </view>\n          </view>\n        </view>\n      </view>\n      <view class=\"btnBox\">\n        <button\n          class=\"add_btn\"\n          type=\"primary\"\n          plain=\"true\"\n          @click=\"handleSaveRemark\"\n        >\n          完成\n        </button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapState, mapMutations } from \"vuex\";\nexport default {\n  data() {\n    return {\n      remark: \"\",\n      numVal: 0,\n    };\n  },\n  computed: {\n    getVal: function () {\n      let leng = this.validateTextLength(this.remark);\n      if (leng <= 50) {\n        this.numVal = Math.floor(leng);\n      } else {\n        this.remark = this.remark.substring(0, 50);\n      }\n    },\n  },\n  onLoad() {\n    console.log(this.remarkData());\n    if (this.getRemark === \"\") {\n      this.remark = this.remark;\n    } else {\n      this.remark = this.remarkData();\n      this.numVal = this.remark.length;\n    }\n  },\n  methods: {\n    ...mapMutations([\"setRemark\"]),\n    ...mapState([\"remarkData\"]),\n    goBack() {\n      uni.redirectTo({\n        url: \"/pages/order/index\",\n      });\n    },\n    // 保存返回订单页\n    handleSaveRemark() {\n      uni.redirectTo({\n        url: \"/pages/order/index\",\n      });\n      this.setRemark(this.remark);\n    },\n    validateTextLength(value) {\n      // 中文、中文标点、全角字符按1长度，英文、英文符号、数字按0.5长度计算\n      let cnReg = /([\\u4e00-\\u9fa5]|[\\u3000-\\u303F]|[\\uFF00-\\uFF60])/g;\n      let mat = value.match(cnReg);\n      let length;\n      if (mat) {\n        length = mat.length + (value.length - mat.length) * 0.5;\n        return length;\n      } else {\n        return value.length * 0.5;\n      }\n    },\n  },\n};\n</script>\n\n<style src=\"./../common/Navbar/navbar.scss\" lang=\"scss\" scoped></style>\n<style src=\"./../order/style.scss\" lang=\"scss\"></style>\n"], "sourceRoot": ""}