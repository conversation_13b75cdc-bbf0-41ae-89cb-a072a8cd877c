{"version": 3, "sources": ["webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/common/Navbar/navbar.vue?c077", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/common/Navbar/navbar.vue?c1df", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/common/Navbar/navbar.vue?6d71", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/common/Navbar/navbar.vue?7255", "uni-app:///pages/common/Navbar/navbar.vue"], "names": ["computed", "ht", "methods", "myCenterFun", "uni", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACe;;;AAGpE;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8xB,CAAgB,gxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCelzB;EACAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/common/Navbar/navbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./navbar.vue?vue&type=template&id=e72ca7b8&\"\nvar renderjs\nimport script from \"./navbar.vue?vue&type=script&lang=js&\"\nexport * from \"./navbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./navbar.scss?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/common/Navbar/navbar.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./navbar.vue?vue&type=template&id=e72ca7b8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./navbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./navbar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"navBar\">\n\t\t<!-- 个人中心 -->\n\t\t<view @click=\"myCenterFun\" class=\"person-box\" :style=\"{top:ht + 'px'}\">\n\t\t\t<image class=\"test_image\" src=\"../../../static/center.png\"></image>\n\t\t\t<span class=\"person-title\">个人中心</span>\n\t\t</view>\n\t\t<image class=\"index_bg\" src=\"../../../static/bg.png\"></image>\n\t\t<!-- <view class=\"leftNav\" >\n\t\t\t<image class=\"logo\" src=\"/static/logo.png\"></image>\n\t\t</view> -->\n\t</view>\n</template>\n\n<script>\nexport default {\n\tcomputed: {\n\t\t// ht: function () {\n\t\t// \tlet res = uni.getMenuButtonBoundingClientRect()\n\t\t// \tlet num = 24\n\t\t// \tif(/iPhone.*/.test(uni.getSystemInfoSync().model)){\n\t\t// \t\t num = res.top * 1.6\n\t\t// \t} else {\n\t\t// \t\tnum = res.top * 2\n\t\t// \t}\n\t\t// \treturn num\n\t\t// }\n\t\tht: function () {\n\t\t\tlet res = uni.getMenuButtonBoundingClientRect() \n\t\t\treturn res.top +5\n\t\t}\n\t},\n\tmethods: {\n\t\tmyCenterFun () {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/my/my'\n\t\t\t})\n\t\t}\n\t}\n}\n</script>\n\n<style src=\"./navbar.scss\" lang=\"scss\"></style>\n"], "sourceRoot": ""}