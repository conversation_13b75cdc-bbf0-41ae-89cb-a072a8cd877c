{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/pay/index.vue?7742", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/pay/index.vue?c68f", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/pay/index.vue?3a82", "webpack:///D:/桌面/diancan/xiaochengxu/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/pay/index.vue?3bfe", "uni-app:///pages/pay/index.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "timeout", "rocallTime", "orderId", "orderDataInfo", "activeRadio", "payMethodList", "times", "created", "mounted", "onLoad", "methods", "handleSave", "uni", "url", "clearTimeout", "orderNumber", "payMethod", "res", "package", "err", "payRes", "console", "title", "icon", "setTimeout", "duration", "runTimeBack", "min", "sec", "that"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACyD;AACjC;;;AAG5E;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,+wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC0DjzB;AACA;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC,yCACA;IACA;IACAC;MAAA;MACA;QACA;QACAC;UACAC;QACA;MACA;QACA;QACAC;QACA;UACAC;UACAC;QACA;QACA;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,MACAC;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OACAL,mDACAK;sBACAC;oBAAA,GACA;kBAAA;oBAAA;oBAAA;oBAHAC;oBAAAC;oBAIAC;oBAAA,KACAF;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OACAP;sBAAAU;sBAAAC;oBAAA;kBAAA;oBACAC;sBACA;sBACAZ;wBACAC;sBACA;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA,OAEAD;sBAAAU;sBAAAC;oBAAA;kBAAA;oBACAC;sBACA;sBACAZ;wBACAC;sBACA;oBACA;kBAAA;oBAAA;oBAAA;kBAAA;oBAGAD;sBACAU;sBACAG;sBACAF;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAAA;YAAA;UAAA;QAAA;MACA;IACA;IACA;IACAG;MACA;MACA;MACA;MACA;MACA;QACA;QACAZ;MACA;QACA;QACA;QACA;UACAa;QACA;UACAA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA;QACA;QACA;UACA;YACA;YACAd;YACA;UACA;UACA;YACAe;UACA;QACA;MACA;IACA;EAAA;AAEA;AAAA,2B", "file": "pages/pay/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pay/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=32f2f1fc&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./../common/Navbar/navbar.scss?vue&type=style&index=0&id=32f2f1fc&lang=scss&scoped=true&\"\nimport style1 from \"./../order/style.scss?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"32f2f1fc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pay/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=32f2f1fc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.shopInfo()\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<!--购买页-->\n<template>\n  <view class=\"customer-box\">\n    <view class=\"wrap\">\n      <view class=\"contion\">\n        <view class=\"orderPay\">\n          <view>\n            <view v-if=\"timeout\">订单已超时</view>\n            <view v-else\n              >支付剩余时间<text>{{ rocallTime }}</text></view\n            >\n          </view>\n          <view class=\"money\"\n            >￥<text>{{ orderDataInfo.orderAmount }}</text></view\n          >\n          <view>{{ shopInfo().shopName }}-{{ orderDataInfo.orderNumber }}</view>\n        </view>\n      </view>\n      <view class=\"box payBox\">\n        <view class=\"contion\">\n          <view class=\"example-body\">\n            <radio-group class=\"uni-list\" @change=\"styleChange\">\n              <view class=\"uni-list-item\">\n                <view\n                  class=\"uni-list-item__container\"\n                  v-for=\"(item, index) in payMethodList\"\n                  :key=\"item\"\n                >\n                  <view class=\"uni-list-item__content\">\n                    <icon class=\"wechatIcon\"></icon\n                    ><text class=\"uni-list-item__content-title\">{{\n                      item\n                    }}</text>\n                  </view>\n                  <view class=\"uni-list-item__extra\">\n                    <radio\n                      :value=\"item\"\n                      color=\"#FFC200\"\n                      :checked=\"index == activeRadio\"\n                      class=\"radioIcon\"\n                    />\n                  </view>\n                </view>\n              </view>\n            </radio-group>\n          </view>\n        </view>\n      </view>\n      <view class=\"bottomBox btnBox\">\n        <button class=\"add_btn\" type=\"primary\" plain=\"true\" @click=\"handleSave\">\n          确认支付\n        </button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapState } from \"vuex\";\nimport { paymentOrder, cancelOrder } from \"@/pages/api/api.js\";\nexport default {\n  data() {\n    return {\n      timeout: false,\n      rocallTime: \"\",\n      orderId: null,\n      orderDataInfo: {},\n      activeRadio: 0,\n      payMethodList: [\"微信支付\"],\n      times: null,\n    };\n  },\n  created() {\n    this.orderDataInfo = this.orderData();\n  },\n  mounted() {\n    this.runTimeBack();\n  },\n  onLoad(options) {\n    this.orderId = options.orderId;\n  },\n  methods: {\n    ...mapState([\"orderData\", \"shopInfo\"]),\n    // 支付详情\n    handleSave() {\n      if (this.timeout) {\n        cancelOrder(this.orderId).then((res) => {});\n        uni.redirectTo({\n          url: \"/pages/details/index?orderId=\" + this.orderId,\n        });\n      } else {\n        // 如果支付成功进入成功页\n        clearTimeout(this.times);\n        const params = {\n          orderNumber: this.orderDataInfo.orderNumber,\n          payMethod: this.activeRadio === 0 ? 1 : 2,\n        };\n        paymentOrder(params).then(async (res) => {\n          if (res.code === 1) {\n            const [err, payRes] = await uni.requestPayment({\n              ...res.data,\n              package: res.data.packageStr, // package 为微信支付必须的字段\n            });\n            console.log(err, payRes);\n            if (err) {\n              await uni.showToast({ title: \"支付失败\", icon: \"error\" });\n              setTimeout(() => {\n                // 下单失败!!\n                uni.redirectTo({\n                  url: \"/pages/details/index?orderId=\" + this.orderId,\n                });\n              }, 1500);\n            } else {\n              await uni.showToast({ title: \"支付成功\", icon: \"success\" });\n              setTimeout(() => {\n                // 下单成功!!\n                uni.redirectTo({\n                  url: \"/pages/success/index?orderId=\" + this.orderId,\n                });\n              }, 1500);\n            }\n          } else {\n            uni.showToast({\n              title: res.msg,\n              duration: 1000,\n              icon: \"none\",\n            });\n          }\n        });\n      }\n    },\n    // // 订单倒计时\n    runTimeBack() {\n      const end = Date.parse(this.orderDataInfo.orderTime.replace(/-/g, \"/\"));\n      const now = Date.parse(new Date());\n      const m15 = 15 * 60 * 1000;\n      const msec = m15 - (now - end);\n      if (msec < 0) {\n        this.timeout = true;\n        clearTimeout(this.times);\n      } else {\n        let min = parseInt((msec / 1000 / 60) % 60);\n        let sec = parseInt((msec / 1000) % 60);\n        if (min < 10) {\n          min = \"0\" + min;\n        } else {\n          min = min;\n        }\n        if (sec < 10) {\n          sec = \"0\" + sec;\n        } else {\n          sec = sec;\n        }\n        this.rocallTime = min + \":\" + sec;\n        let that = this;\n        if (min >= 0 && sec >= 0) {\n          if (min === 0 && sec === 0) {\n            this.timeout = true;\n            clearTimeout(this.times);\n            return;\n          }\n          this.times = setTimeout(function () {\n            that.runTimeBack();\n          }, 1000);\n        }\n      }\n    },\n  },\n};\n</script>\n<style src=\"./../common/Navbar/navbar.scss\" lang=\"scss\" scoped></style>\n<style src=\"./../order/style.scss\" lang=\"scss\"></style>\n<style>\n</style>\n"], "sourceRoot": ""}