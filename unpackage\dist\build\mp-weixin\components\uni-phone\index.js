(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-phone/index"],{"0a0c":function(n,t,u){"use strict";u.d(t,"b",(function(){return o})),u.d(t,"c",(function(){return c})),u.d(t,"a",(function(){return e}));var e={uniPopup:function(){return u.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(u.bind(null,"d9af"))}},o=function(){var n=this.$createElement;this._self._c},c=[]},b237:function(n,t,u){"use strict";u.r(t);var e=u("f3a8"),o=u.n(e);for(var c in e)["default"].indexOf(c)<0&&function(n){u.d(t,n,(function(){return e[n]}))}(c);t["default"]=o.a},c18c:function(n,t,u){"use strict";u.r(t);var e=u("0a0c"),o=u("b237");for(var c in o)["default"].indexOf(c)<0&&function(n){u.d(t,n,(function(){return o[n]}))}(c);u("2c18");var i=u("828b"),a=Object(i["a"])(o["default"],e["b"],e["c"],!1,null,"26c6f860",null,!1,e["a"],void 0);t["default"]=a.exports},f3a8:function(n,t,u){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e=u("b99c"),o={props:{phoneData:{type:String,default:""}},methods:{call:function(){(0,e.call)(this.phoneData)},closePopup:function(){this.$emit("closePopup")}}};t.default=o}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-phone/index-create-component',
    {
        'components/uni-phone/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("c18c"))
        })
    },
    [['components/uni-phone/index-create-component']]
]);
