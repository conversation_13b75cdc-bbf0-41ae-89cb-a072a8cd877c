(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-icons/uni-icons"],{"19c3":function(n,t,e){"use strict";var u=e("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=u(e("d95a")),c={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16}},data:function(){return{icons:i.default}},methods:{_onClick:function(){this.$emit("click")}}};t.default=c},"1f73":function(n,t,e){"use strict";e.r(t);var u=e("19c3"),i=e.n(u);for(var c in u)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(c);t["default"]=i.a},"2a66":function(n,t,e){"use strict";e.d(t,"b",(function(){return u})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){}));var u=function(){var n=this.$createElement;this._self._c},i=[]},b8e1:function(n,t,e){"use strict";var u=e("f8f4"),i=e.n(u);i.a},c9d4:function(n,t,e){"use strict";e.r(t);var u=e("2a66"),i=e("1f73");for(var c in i)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(c);e("b8e1");var a=e("828b"),f=Object(a["a"])(i["default"],u["b"],u["c"],!1,null,"1217d9c2",null,!1,u["a"],void 0);t["default"]=f.exports},f8f4:function(n,t,e){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-icons/uni-icons-create-component',
    {
        'components/uni-icons/uni-icons-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("c9d4"))
        })
    },
    [['components/uni-icons/uni-icons-create-component']]
]);
