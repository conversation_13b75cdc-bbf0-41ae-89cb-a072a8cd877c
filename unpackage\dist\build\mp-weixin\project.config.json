{"description": "项目配置文件。", "packOptions": {"ignore": []}, "setting": {"urlCheck": false, "es6": false, "postcss": false, "minified": true, "newFeature": true, "bigPackageSizeSupport": true, "minifyWXML": true}, "compileType": "miniprogram", "libVersion": "", "appid": "wx2562652ea619766f", "projectname": "sky-take-out-user-mp", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}}