(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/products/product4/index"],{"52b4":function(n,t,e){"use strict";e.d(t,"b",(function(){return a})),e.d(t,"c",(function(){return c})),e.d(t,"a",(function(){}));var a=function(){var n=this.$createElement;this._self._c},c=[]},6119:function(n,t,e){"use strict";e.r(t);var a=e("52b4"),c=e("6499");for(var u in c)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return c[n]}))}(u);e("f32a");var r=e("828b"),o=Object(r["a"])(c["default"],a["b"],a["c"],!1,null,"ef3af2e6",null,!1,a["a"],void 0);t["default"]=o.exports},adc9:function(n,t,e){"use strict";(function(n,t){var a=e("47a9");e("6134");a(e("3240"));var c=a(e("6119"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(c.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["adc9","common/runtime","common/vendor"]]]);