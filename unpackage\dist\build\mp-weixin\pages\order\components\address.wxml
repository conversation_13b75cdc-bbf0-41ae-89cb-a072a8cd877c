<view class="container new_address data-v-1834e562"><view data-event-opts="{{[['tap',[['goAddress',['$event']]]]]}}" class="top data-v-1834e562" bindtap="__e"><block wx:if="{{!address}}"><view class="address_name_disabled data-v-1834e562">请选择收货地址</view></block><block wx:else><view class="address_name data-v-1834e562"><view class="address data-v-1834e562"><text class="{{['tag','data-v-1834e562','tag'+tagLabel]}}">{{addressLabel}}</text><text class="word data-v-1834e562">{{address}}</text></view><view class="name data-v-1834e562"><text class="name_1 data-v-1834e562">{{cryptoName}}</text><text class="name_2 data-v-1834e562">{{phoneNumber}}</text></view><block wx:if="{{address}}"><view class="infoTip data-v-1834e562">为减少接触，降低风险，推荐使用无接触配送</view></block></view></block><view class="address_image data-v-1834e562"><view class="to_right data-v-1834e562"></view></view></view><view class="bottom data-v-1834e562"><view data-event-opts="{{[['tap',[['openTimePopuo',['bottom']]]]]}}" class="bottomTime _div data-v-1834e562" bindtap="__e"><text class="time_name_disabled data-v-1834e562">立即送出</text><view class="address_image data-v-1834e562"><text class="data-v-1834e562">{{arrivalTime+"送达"}}</text><view class="to_right data-v-1834e562"></view></view></view><block wx:if="{{address}}"><view class="infoTip data-v-1834e562">因配送订单较多，送达时间可能波动</view></block></view><uni-popup class="popupBox data-v-1834e562 vue-ref" bind:change="__e" vue-id="e73d885e-1" data-ref="timePopup" data-event-opts="{{[['^change',[['change']]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-1834e562"><view class="pickerCon data-v-1834e562"><view class="dayBox data-v-1834e562"><scroll-view scroll-x="true" scroll-into-view="{{scrollinto}}" scroll-with-animation="{{true}}" class="data-v-1834e562"><block wx:for="{{popleft}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="scroll-row-item data-v-1834e562" id="{{'tab'+index}}" data-event-opts="{{[['tap',[['dateChange',[index]]]]]}}" bindtap="__e"><block wx:for="{{weeks}}" wx:for-item="val" wx:for-index="i" wx:key="i"><view class="data-v-1834e562"><block wx:if="{{index===i}}"><view class="{{['data-v-1834e562',tabIndex==index?'scroll-row-day':'']}}"><text class="line data-v-1834e562"></text>{{item}}<text class="week data-v-1834e562">{{"("+val+")"}}</text></view></block></view></block></view></block></scroll-view></view><view class="timeBox data-v-1834e562"><scroll-view class="card_order_list data-v-1834e562" scroll-y="true" scroll-top="40rpx"><block wx:for="{{newDateData}}" wx:for-item="val" wx:for-index="i" wx:key="i"><view data-event-opts="{{[['tap',[['timeClick',['$0',i],[[['newDateData','',i]]]]]]]}}" class="{{['item','data-v-1834e562',selectValue===i?'city-column_select':'']}}" bindtap="__e">{{val}}</view></block></scroll-view></view></view><view data-event-opts="{{[['tap',[['onsuer',['$event']]]]]}}" class="btns data-v-1834e562" bindtap="__e">取消</view></view></uni-popup></view>